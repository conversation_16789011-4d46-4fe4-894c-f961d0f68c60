const fs = require('fs');

// 测试配置文件读取
const confPath = './conf.json'; // 使用本地配置文件进行测试

try {
  console.log('Testing configuration file reading...');
  
  const confContent = fs.readFileSync(confPath, 'utf8');
  const conf = JSON.parse(confContent);
  
  console.log('✅ Configuration file loaded successfully');
  console.log(`Gateway paths: ${JSON.stringify(conf.gateway_paths)}`);
  console.log(`Number of models: ${Object.keys(conf.models || {}).length}`);
  console.log(`Number of configurations: ${Object.keys(conf.configurations || {}).length}`);
  console.log(`Number of providers: ${Object.keys(conf.providers || {}).length}`);
  
  // 测试一个具体的模型配置
  const testModel = 'gpt-4o-mini';
  if (conf.models[testModel]) {
    console.log(`\n✅ Test model '${testModel}' found:`);
    console.log(`  Config name: ${conf.models[testModel]['config-name']}`);
    console.log(`  Use in: ${JSON.stringify(conf.models[testModel]['use-in'])}`);
    console.log(`  Type: ${JSON.stringify(conf.models[testModel].type)}`);
    
    const configKey = conf.models[testModel]['config-name'];
    if (conf.configurations[configKey]) {
      console.log(`  ✅ Configuration '${configKey}' found`);
    } else {
      console.log(`  ❌ Configuration '${configKey}' not found`);
    }
  } else {
    console.log(`❌ Test model '${testModel}' not found`);
  }
  
} catch (error) {
  console.error('❌ Error reading configuration file:', error.message);
}
