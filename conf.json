{"configurations": {"common-config-1": {"strategy": {"mode": "fallback"}, "targets": [{"strategy": {"mode": "loadbalance"}, "targets": ["github", "github1", "github2", "github3"]}, {"strategy": {"mode": "fallback"}, "targets": [{"strategy": {"mode": "loadbalance"}, "targets": ["hyw1mini", "azure_edu_4omini", "lzj2mini", "ham1mini", "ham2mini", "chatanywhere", "wochirou1", "itsfurry", "gueai", "aischat", "crond", "<PERSON>hair"]}, {"strategy": {"mode": "fallback"}, "targets": ["azure_edu_4omini", "<PERSON>hair"]}]}, {"added_params": {"override_params": {"model": "gemini-1.5-flash"}}, "base_provider": "gemini"}]}, "common-config-10": "searx", "common-config-11": "vertex-ai", "common-config-12": "<PERSON><PERSON><PERSON><PERSON>", "common-config-13": {"strategy": {"mode": "fallback"}, "targets": [{"strategy": {"mode": "loadbalance"}, "targets": ["github", "github1", "github2", "github3"]}, "azure_edu_4omini"]}, "common-config-14": {"retry": {"attempts": 4}, "strategy": {"mode": "loadbalance"}, "targets": ["gemini", "gemini2", "gemini3", "gemini4", "gemini5", "gemini6", "gemini7", "gemini8"]}, "common-config-15": "azure_edu_4", "common-config-17": "flux", "common-config-18": "bing", "common-config-19": {"retry": {"attempts": 4}, "strategy": {"mode": "loadbalance"}, "targets": ["github2"]}, "common-config-2": {"strategy": {"mode": "fallback"}, "targets": [{"retry": {"attempts": 2}, "strategy": {"mode": "loadbalance"}, "targets": ["github", "github1", "github2", "github3"]}, {"strategy": {"mode": "fallback"}, "targets": [{"strategy": {"mode": "loadbalance"}, "targets": ["gueai", "wochirou1", "wochirou2", "azure_edu_4o", "itsfurry", "<PERSON>hair"]}, {"strategy": {"mode": "fallback"}, "targets": ["azure_edu_4o", "<PERSON>hair"]}]}]}, "common-config-20": {"strategy": {"mode": "fallback"}, "targets": ["perplexity"]}, "common-config-21": "mistral", "common-config-22": {"strategy": {"mode": "fallback"}, "targets": ["claude03", "claude04", "claude05", "claude06"]}, "common-config-23": {"strategy": {"mode": "loadbalance"}, "targets": ["azure_edu_4", "itsfurry"]}, "common-config-24": "bazi", "common-config-25": "libre", "common-config-26": "deepseek", "common-config-27": {"strategy": {"mode": "fallback"}, "targets": [{"strategy": {"mode": "loadbalance"}, "targets": ["github", "github1", "github2", "github3"]}, "azure_edu_4o", "<PERSON>hair"]}, "common-config-28": {"added_params": {"override_params": {"max_tokens": 8192, "model": "gemini-1.5-flash-002"}}, "base_provider": "vertex-ai"}, "common-config-29": {"added_params": {"override_params": {"max_tokens": 8192, "model": "text-multilingual-embedding-002"}}, "base_provider": "vertex-ai"}, "common-config-3": {"strategy": {"mode": "fallback"}, "targets": [{"strategy": {"mode": "loadbalance"}, "targets": ["github", "github1", "github2", "github3"]}, {"strategy": {"mode": "loadbalance"}, "targets": ["hyw1mini", "voapi", "lzj2mini", "chatanywhere", "wochirou1", "itsfurry", "<PERSON>hair"]}, "itsfurry", "<PERSON>hair"]}, "common-config-30": "siliconflow1", "common-config-31": "spark", "common-config-32": "hun<PERSON>", "common-config-33": "internlm", "common-config-34": "cloudflare", "common-config-35": "groq", "common-config-36": {"added_params": {"override_params": {"model": "BAAI/bge-large-zh-v1.5"}}, "base_provider": "siliconflow1"}, "common-config-37": {"added_params": {"override_params": {"max_tokens": 8192, "model": "imagen-3.0-generate-001"}}, "base_provider": "vertex-ai"}, "common-config-38": "<PERSON><PERSON><PERSON>", "common-config-39": {"strategy": {"mode": "fallback"}, "targets": ["4chat-copy", "jpny", "crond", "aischat"]}, "common-config-4": {"strategy": {"mode": "fallback"}, "targets": ["voapi", {"strategy": {"mode": "loadbalance"}, "targets": ["crond", "itsfurry"]}, "azure_edu_4", {"added_params": {"override_params": {"model": "gemini-1.5-pro"}}, "base_provider": "gemini"}]}, "common-config-41": {"strategy": {"mode": "fallback"}, "targets": [{"added_params": {"override_params": {"model": "azure-deepseek-r1"}}, "base_provider": "mult-r1"}, "electronhub", "yunwu"]}, "common-config-411": "x-ai", "common-config-42": {"strategy": {"mode": "fallback"}, "targets": ["deepseek-api", {"added_params": {"override_params": {"model": "deepseek-v3"}}, "base_provider": "electronhub"}]}, "common-config-422": "deepseek-api", "common-config-43": "cohere", "common-config-44": {"added_params": {"override_params": {"model": "ep-20250205102805-nsv4s"}}, "base_provider": "huo<PERSON>"}, "common-config-45": "mult-r1", "common-config-46": {"strategy": {"mode": "fallback"}, "targets": [{"added_params": {"override_params": {"model": "spn3/DeepSeek-chat"}}, "base_provider": "sophnet1"}, {"added_params": {"override_params": {"model": "ep-20250326171018-jlftl"}}, "base_provider": "huo<PERSON>"}, {"added_params": {"override_params": {"model": "spn3/DeepSeek-chat"}}, "base_provider": "sophnet2"}, {"added_params": {"override_params": {"model": "deepseek-ai/DeepSeek-V3-0324"}}, "base_provider": "chutes"}, {"added_params": {"override_params": {"model": "deepseek/deepseek-chat-v3-0324:free"}}, "base_provider": "openrouter"}, {"added_params": {"override_params": {"model": "deepseek-v3-deepinfra"}}, "base_provider": "mult-r1"}, {"added_params": {"override_params": {"model": "deepseek-ai/DeepSeek-V3-0324"}}, "base_provider": "nebius"}, {"added_params": {"override_params": {"model": "DeepSeek-V3"}}, "base_provider": "github"}, "mult-r1", "electronhub", "yunwu"]}, "common-config-47": {"retry": {"attempts": 2}, "strategy": {"mode": "fallback"}, "targets": [{"added_params": {"override_params": {"model": "azure-deepseek-r1"}}, "base_provider": "mult-r1"}, {"added_params": {"override_params": {"model": "huoshan-deepseek-r1"}}, "base_provider": "mult-r1"}, {"added_params": {"override_params": {"model": "openrouter-deepseek-r1"}}, "base_provider": "mult-r1"}, {"added_params": {"override_params": {"model": "novita-deepseek-r1"}}, "base_provider": "mult-r1"}, {"added_params": {"override_params": {"model": "deepinfra-deepseek-r1"}}, "base_provider": "mult-r1"}, {"added_params": {"override_params": {"model": "xunfei-deepseek-r1"}}, "base_provider": "mult-r1"}, {"added_params": {"override_params": {"model": "huawei-deepseek-r1"}}, "base_provider": "mult-r1"}, {"added_params": {"override_params": {"model": "siliconflow-deepseek-r1"}}, "base_provider": "mult-r1"}]}, "common-config-48": "deepinfra", "common-config-49": "electronhub", "common-config-5": "a<PERSON><PERSON>", "common-config-50": "hamA1-grok", "common-config-51": "genspark", "common-config-52": "ephone-offical", "common-config-53": "chat01-wrhsdoe", "common-config-54": "openxs", "common-config-55": "ephone", "common-config-56": "gueai-nixiang", "common-config-57": "voapi-default", "common-config-58": {"strategy": {"mode": "loadbalance"}, "targets": []}, "common-config-59": "haochi", "common-config-6": {"strategy": {"mode": "fallback"}, "targets": ["voapi", "electronhub", "gueai1"]}, "common-config-61": "gueai-default", "common-config-62": "grok-api", "common-config-63": "fal", "common-config-64": "openai", "common-config-65": {"added_params": {"override_params": {"max_tokens": 64000}}, "base_provider": "anthropic"}, "common-config-66": "crewdle@3.7", "common-config-67": "julep", "common-config-68": {"added_params": {"override_params": {"max_tokens": 64000}}, "base_provider": "mdb"}, "common-config-69": "gemini-copy", "common-config-7": {"strategy": {"mode": "fallback"}, "targets": ["voapi", "itsfurry", "<PERSON>hair", "wochirou1", "burnhair-retry"]}, "common-config-71": {"added_params": {"override_params": {"model": "deepseek-ai/DeepSeek-R1-0528"}}, "base_provider": "nebius"}, "common-config-8": "anthropic", "common-config-9": {"strategy": {"mode": "fallback"}, "targets": [{"strategy": {"mode": "loadbalance"}, "targets": ["github", "github1", "github2", "github3"]}, "azure_edu_4o", {"added_params": {"override_params": {"model": "gemini-1.5-pro"}}, "base_provider": "gemini"}, {"added_params": {"override_params": {"model": "pixtral-large-latest"}}, "base_provider": "mistral"}]}, "common-config-72": {"strategy": {"mode": "fallback"}, "retry": {"attempts": 2}, "targets": [{"base_provider": "nebius", "added_params": {"override_params": {"model": "zai-org/GLM-4.5"}, "trans-type": ["reasoning_content"]}}, {"base_provider": "modelscope1", "added_params": {"trans-type": ["reasoning_content"], "override_params": {"model": "ZhipuAI/GLM-4.5"}}}, {"base_provider": "sophnet2", "added_params": {"trans-type": ["reasoning_content"], "override_params": {"model": "GLM-4.5"}}}]}, "common-config-73": {"strategy": {"mode": "fallback"}, "retry": {"attempts": 2}, "targets": [{"base_provider": "modelscope1", "added_params": {"override_params": {"max_tokens": 256000, "model": "moonshotai/Kimi-K2-Instruct-0905"}}}, {"base_provider": "openrouter", "added_params": {"override_params": {"model": "moonshotai/kimi-k2:free"}}}, {"base_provider": "sophnet2", "added_params": {"override_params": {"model": "Kimi-K2"}}}, {"base_provider": "groq", "added_params": {"override_params": {"max_tokens": 16384, "model": "moonshotai/kimi-k2-instruct-0905"}}}], "added_params": {"trans-type": ["add_think_start"]}}, "common-config-74": {"strategy": {"mode": "fallback"}, "retry": {"attempts": 2}, "targets": [{"base_provider": "cerebas", "added_params": {"override_params": {"model": "qwen-3-235b-a22b-instruct-2507"}}}, {"base_provider": "modelscope1", "added_params": {"override_params": {"model": "Qwen/Qwen3-235B-A22B-Instruct-2507"}}}, {"base_provider": "sophnet2", "added_params": {"override_params": {"model": "Qwen3-235B-A22B-Instruct-2507"}}}, {"base_provider": "nebius", "added_params": {"override_params": {"model": "Qwen/Qwen3-235B-A22B-Instruct-2507"}}}]}, "common-config-75": {"strategy": {"mode": "fallback"}, "retry": {"attempts": 2}, "targets": [{"base_provider": "cerebas", "added_params": {"override_params": {"model": "qwen-3-235b-a22b-thinking-2507"}, "trans-type": ["add_think_start"]}}, {"base_provider": "modelscope1", "added_params": {"override_params": {"model": "Qwen/Qwen3-235B-A22B-Thinking-2507"}}}], "added_params": {"trans-type": ["reasoning_content"]}}, "common-config-76": {"strategy": {"mode": "fallback"}, "retry": {"attempts": 2}, "targets": [{"base_provider": "cerebas", "added_params": {"override_params": {"model": "qwen-3-coder-480b"}}}, {"base_provider": "modelscope1", "added_params": {"override_params": {"model": "Qwen/Qwen3-Coder-480B-A35B-Instruct"}}}, {"base_provider": "openrouter", "added_params": {"override_params": {"model": "qwen/qwen3-coder:free"}}}, {"base_provider": "sophnet2", "added_params": {"override_params": {"model": "Qwen3-Coder"}}}]}, "common-config-77": "azure_edu_5_mini", "common-config-78": "azure_edu_5_chat", "common-config-79": {"strategy": {"mode": "fallback"}, "retry": {"attempts": 2}, "targets": [{"base_provider": "modelscope1", "added_params": {"override_params": {"model": "openai-mirror/gpt-oss-120b"}}}, {"base_provider": "cerebas", "added_params": {"override_params": {"model": "gpt-oss-120b"}}}, {"base_provider": "nebius", "added_params": {"override_params": {"model": "openai/gpt-oss-120b"}, "trans-type": ["reasoning_content"]}}]}, "common-config-80": {"strategy": {"mode": "fallback"}, "retry": {"attempts": 2}, "targets": [{"base_provider": "github", "added_params": {"override_params": {"model": "openai/gpt-5"}}}]}, "common-config-81": {"strategy": {"mode": "fallback"}, "retry": {"attempts": 2}, "targets": [{"base_provider": "modelscope1", "added_params": {"override_params": {"model": "deepseek-ai/DeepSeek-V3.1"}}}, {"base_provider": "sophnet2", "added_params": {"override_params": {"model": "DeepSeek-V3.1"}}}, {"base_provider": "akash", "added_params": {"override_params": {"model": "DeepSeek-V3-1"}}}, {"base_provider": "deepinfra", "added_params": {"override_params": {"model": "deepseek-ai/DeepSeek-V3.1"}}}, {"base_provider": "huo<PERSON>", "added_params": {"override_params": {"model": "ep-20250822151009-gn98z"}}}]}, "common-config-82": "fal-gemini", "common-config-83": {"strategy": {"mode": "fallback"}, "retry": {"attempts": 2}, "targets": [{"base_provider": "modelscope1", "added_params": {"override_params": {"model": "Qwen/Qwen3-Next-80B-A3B-Instruct"}}}, {"base_provider": "sophnet2", "added_params": {"override_params": {"model": "Qwen3-Next-80B-A3B-Instruct"}}}, {"base_provider": "deepinfra", "added_params": {"override_params": {"model": "Qwen/Qwen3-Next-80B-A3B-Instruct"}}}, {"base_provider": "akash", "added_params": {"override_params": {"model": "Qwen/Qwen3-Next-80B-A3B-Instruct"}}}]}, "common-config-84": {"strategy": {"mode": "fallback"}, "retry": {"attempts": 2}, "targets": [{"base_provider": "sophnet2", "added_params": {"override_params": {"model": "Qwen3-Next-80B-A3B-Thinking"}}}, {"base_provider": "deepinfra", "added_params": {"override_params": {"model": "Qwen/Qwen3-Next-80B-A3B-Thinking"}}}]}, "common-config-85": {"strategy": {"mode": "fallback"}, "retry": {"attempts": 2}, "targets": [{"base_provider": "sophnet2", "added_params": {"override_params": {"model": "LongCat-Flash-Chat"}}}]}, "common-config-86": "siliconflow1"}, "gateway_paths": ["gateway", "test", "xdk"], "models": {"@cf/meta/llama-3.1-70b-instruct": {"config-name": "common-config-34", "use-in": [], "type": ["stop"]}, "@cf/meta/llama-3.2-3b-instruct": {"config-name": "common-config-34", "use-in": [], "type": ["stop"]}, "AIDC-AI/Marco-o1": {"config-name": "common-config-30", "use-in": [], "type": ["stop"]}, "BAAI/bge-large-en-v1.5": {"config-name": "common-config-30", "use-in": [], "type": ["stop"]}, "BAAI/bge-large-zh-v1.5": {"config-name": "common-config-30", "use-in": [], "type": ["stop"]}, "BAAI/bge-m3": {"config-name": "common-config-30", "use-in": [], "type": ["stop"]}, "BAAI/bge-reranker-v2-m3": {"config-name": "common-config-30", "use-in": [], "type": ["stop"]}, "Qwen/Qwen2.5-7B-Instruct": {"config-name": "common-config-30", "use-in": [], "type": ["stop"]}, "THUDM/glm-4-9b-chat": {"config-name": "common-config-30", "use-in": [], "type": ["stop"]}, "anthropic/claude-3.5-sonnet": {"config-name": "common-config-63", "use-in": [], "type": ["chat"]}, "anthropic/claude-3.7-sonnet": {"config-name": "common-config-63", "use-in": [], "type": ["chat"]}, "azure-deepseek-r1": {"config-name": "common-config-45", "use-in": ["gateway"], "type": ["chat"]}, "bazi": {"config-name": "common-config-24", "use-in": [], "type": ["stop"]}, "bazi-pro": {"config-name": "common-config-24", "use-in": [], "type": ["stop"]}, "bingimg": {"config-name": "common-config-17", "use-in": [], "type": ["stop"]}, "claude-3-5-sonnet": {"config-name": "common-config-6", "use-in": [], "type": ["chat"]}, "claude-3-5-sonnet-20240620": {"config-name": "common-config-22", "use-in": [], "type": ["chat"]}, "claude-3-7-sonnet": {"config-name": "common-config-51", "type": ["chat"], "use-in": []}, "claude-3-7-sonnet-20250219": {"config-name": "common-config-65", "use-in": ["gateway"], "type": ["chat"]}, "claude-3-7-sonnet-20250219-thinking": {"config-name": "common-config-49", "type": ["chat"], "use-in": []}, "claude-3-7-sonnet-thinking": {"config-name": "common-config-51", "type": ["chat"], "use-in": []}, "claude-3-opus-20240229": {"config-name": "common-config-8", "type": ["chat"], "use-in": []}, "cogvideox-flash": {"config-name": "common-config-38", "type": ["stop"], "use-in": []}, "cogview-3-flash": {"config-name": "common-config-38", "type": ["stop"], "use-in": []}, "command-r-latest": {"config-name": "common-config-43", "type": ["chat"], "use-in": []}, "command-r-plus-latest": {"config-name": "common-config-43", "type": ["chat"], "use-in": ["gateway"]}, "crewdle@claude-3-7": {"config-name": "common-config-66", "type": ["chat"], "use-in": []}, "dall-e-3": {"config-name": "common-config-6", "type": ["image"], "use-in": []}, "dalle3": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "deepinfra-deepseek-r1": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "deepseek": {"config-name": "common-config-26", "type": ["chat"], "use-in": []}, "deepseek-ai/DeepSeek-R1": {"config-name": "common-config-30", "type": ["chat"], "use-in": []}, "deepseek-ai/DeepSeek-V2.5": {"config-name": "common-config-30", "type": ["chat"], "use-in": []}, "deepseek-chat": {"config-name": "common-config-42", "type": ["chat"], "use-in": []}, "deepseek-deepseek-reasoner": {"config-name": "common-config-42", "type": ["chat"], "use-in": []}, "deepseek-r1": {"config-name": "common-config-41", "type": ["chat"], "use-in": []}, "deepseek-r1-0528": {"config-name": "common-config-71", "type": ["chat"], "use-in": ["gateway"]}, "deepseek-r1-stable": {"config-name": "common-config-47", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "deepseek-v3-0324": {"config-name": "common-config-46", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "deepseek-v3-deepinfra": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "deepseek-v3-inference": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "deepseek-v3-novita": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "deepseek-v3-openrouter": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "deepseek-v3-sophnet": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "fal/FLUX-1.1-pro": {"config-name": "common-config-63", "type": ["image"], "use-in": []}, "fal/flux-pro/v1.1-ultra": {"config-name": "common-config-63", "type": ["image"], "use-in": []}, "fal/ideogram": {"config-name": "common-config-63", "type": ["image"], "use-in": []}, "fal/imagen3": {"config-name": "common-config-63", "type": ["image"], "use-in": []}, "fal/playground-v25": {"config-name": "common-config-63", "type": ["image"], "use-in": []}, "fal/recraft-v3": {"config-name": "common-config-63", "type": ["image"], "use-in": []}, "flux": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "flux-1.1-pro": {"config-name": "common-config-45", "type": ["image"], "use-in": []}, "flux-off": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "flux-pro": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "flux-pro-1.1-ultra": {"config-name": "common-config-61", "type": ["image"], "use-in": []}, "flux-pro-max": {"config-name": "common-config-6", "type": ["image"], "use-in": []}, "flux-pro/ultra": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "flux-si": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "fluxdev-si": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "fluxpro": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "fluxpro-off": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "fluxpro-si": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "gemini-1.5-flash": {"config-name": "common-config-14", "type": ["chat"], "use-in": []}, "gemini-1.5-flash-002": {"config-name": "common-config-28", "type": ["chat"], "use-in": []}, "gemini-1.5-flash-8b": {"config-name": "common-config-14", "type": ["chat"], "use-in": []}, "gemini-1.5-pro": {"config-name": "common-config-14", "type": ["chat"], "use-in": []}, "gemini-flash-image-latest": {"config-name": "common-config-14", "type": ["chat"], "use-in": ["gateway"]}, "gemini-vertex-latest": {"config-name": "common-config-11", "type": ["chat"], "use-in": []}, "glm-4-flash": {"config-name": "common-config-38", "type": ["chat"], "use-in": []}, "glm-4v-flash": {"config-name": "common-config-38", "type": ["stop"], "use-in": []}, "gpt-4-5": {"config-name": "common-config-53", "type": ["chat"], "use-in": []}, "gpt-4-turbo": {"config-name": "common-config-4", "type": ["chat"], "use-in": []}, "gpt-4-turbo-2024-04-09": {"config-name": "common-config-15", "type": ["chat"], "use-in": ["gateway"]}, "gpt-4.1": {"config-name": "common-config-64", "type": ["chat"], "use-in": []}, "gpt-4.1@mdb": {"config-name": "common-config-68", "type": ["chat"], "use-in": []}, "gpt-4.5-preview": {"config-name": "common-config-52", "type": ["chat"], "use-in": []}, "gpt-4.5-preview@mdb": {"config-name": "common-config-68", "type": ["chat"], "use-in": []}, "gpt-4o": {"config-name": "common-config-2", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "gpt-4o-all-自有号池": {"config-name": "common-config-58", "type": ["chat"], "use-in": []}, "gpt-4o-all2": {"config-name": "common-config-53", "type": ["chat"], "use-in": []}, "gpt-4o-full": {"config-name": "common-config-23", "type": ["chat"], "use-in": []}, "gpt-4o-func": {"config-name": "common-config-27", "type": ["chat"], "use-in": []}, "gpt-4o-image": {"config-name": "common-config-55", "type": ["chat"], "use-in": []}, "gpt-4o-image-vip": {"config-name": "common-config-56", "type": ["chat"], "use-in": ["gateway"]}, "gpt-4o-mini": {"config-name": "common-config-1", "type": ["chat"], "use-in": ["gateway"]}, "gpt-4o-mini-safe": {"config-name": "common-config-13", "type": ["chat"], "use-in": []}, "gpt-4o-safe": {"config-name": "common-config-9", "type": ["chat"], "use-in": []}, "gpt-4o-tools": {"config-name": "common-config-5", "type": ["chat"], "use-in": []}, "gpt-4o-自有号池": {"config-name": "common-config-59", "type": ["chat"], "use-in": []}, "grok-2": {"config-name": "common-config-50", "type": ["stop"], "use-in": []}, "grok-2-image-latest": {"config-name": "common-config-62", "type": ["chat"], "use-in": []}, "grok-2-imageGen": {"config-name": "common-config-50", "type": ["chat"], "use-in": []}, "grok-2-search": {"config-name": "common-config-50", "type": ["chat"], "use-in": []}, "grok-2-vision-latest": {"config-name": "common-config-62", "type": ["chat"], "use-in": []}, "grok-3": {"config-name": "common-config-50", "type": ["chat"], "use-in": []}, "grok-3-deepersearch": {"config-name": "common-config-50", "type": ["chat"], "use-in": []}, "grok-3-deepsearch": {"config-name": "common-config-50", "type": ["chat"], "use-in": []}, "grok-3-imageGen": {"config-name": "common-config-50", "type": ["chat"], "use-in": []}, "grok-3-latest": {"config-name": "common-config-62", "type": ["chat"], "use-in": []}, "grok-3-mini-latest": {"config-name": "common-config-62", "type": ["chat"], "use-in": []}, "grok-3-reasoning": {"config-name": "common-config-50", "type": ["chat"], "use-in": []}, "grok-3-search": {"config-name": "common-config-50", "type": ["chat"], "use-in": []}, "grok-image": {"config-name": "common-config-17", "type": ["chat"], "use-in": []}, "huawei-deepseek-r1": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "hunyuan-lite": {"config-name": "common-config-32", "type": ["chat"], "use-in": []}, "huoshan-deepseek-r1": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "ideo": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "ideogram": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "ideogram/V_2A": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "image-fx": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "imagen-3": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "imagen-vi": {"config-name": "common-config-37", "type": ["image"], "use-in": []}, "imagen3": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "inference-deepseek-r1": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "internlm2.5-latest": {"config-name": "common-config-33", "type": ["chat"], "use-in": []}, "julep@claude-3.5-sonnet-20241022": {"config-name": "common-config-67", "type": ["chat"], "use-in": []}, "julep@claude-3.7-sonnet": {"config-name": "common-config-67", "type": ["chat"], "use-in": []}, "kimi": {"config-name": "common-config-12", "type": ["chat"], "use-in": []}, "leonardo": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "leonardo-prompt": {"config-name": "common-config-17", "type": ["stop"], "use-in": []}, "libre": {"config-name": "common-config-25", "type": ["stop"], "use-in": []}, "lite": {"config-name": "common-config-31", "type": ["stop"], "use-in": []}, "liuyao": {"config-name": "common-config-24", "type": ["stop"], "use-in": []}, "llama-3.1-70b-versatile": {"config-name": "common-config-35", "type": ["stop"], "use-in": []}, "llama-3.1-8b-instant": {"config-name": "common-config-35", "type": ["stop"], "use-in": []}, "llama3-70b-8192": {"config-name": "common-config-35", "type": ["stop"], "use-in": []}, "llama3-8b-8192": {"config-name": "common-config-35", "type": ["stop"], "use-in": []}, "meta-llama/Meta-Llama-3.1-8B-Instruct": {"config-name": "common-config-30", "type": ["stop"], "use-in": []}, "mistral-image": {"config-name": "common-config-17", "type": ["stop"], "use-in": []}, "mistral-large-latest": {"config-name": "common-config-21", "type": ["chat"], "use-in": ["gateway"]}, "mixtral-8x7b-32768": {"config-name": "common-config-35", "type": ["stop"], "use-in": []}, "novita-deepseek-r1": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "o1": {"config-name": "common-config-51", "type": ["chat"], "use-in": []}, "o1-mini": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "o1-mini1": {"config-name": "common-config-19", "type": ["chat"], "use-in": []}, "o1-preview": {"config-name": "common-config-19", "type": ["chat"], "use-in": []}, "openrouter-deepseek-r1": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "openrouter-qwq-32b": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "openrouter-qwq-32b-deepseek-r1": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "perplexity": {"config-name": "common-config-20", "type": ["chat"], "use-in": []}, "pixtral-large-latest": {"config-name": "common-config-21", "type": ["chat"], "use-in": ["gateway"]}, "playground": {"config-name": "common-config-17", "type": ["stop"], "use-in": []}, "prompt": {"config-name": "common-config-2", "type": ["chat"], "use-in": []}, "r1only-azure-deepseek-r1": {"config-name": "common-config-45", "type": ["chat"], "use-in": ["gateway"]}, "r1with-azure-deepseek-r1@gemini-latest": {"config-name": "common-config-45", "type": ["chat"], "use-in": ["gateway"]}, "recraft": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "recraft-prompt": {"config-name": "common-config-17", "type": ["chat"], "use-in": []}, "recraft-v3": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "sd3.5": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "sd3m": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "sdxl": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "search": {"config-name": "common-config-2", "type": ["stop"], "use-in": []}, "searx": {"config-name": "common-config-10", "type": ["stop"], "use-in": []}, "shengshu": {"config-name": "common-config-17", "type": ["image"], "use-in": []}, "siliconflow-deepseek-r1": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "sophnet-deepseek-r1": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "text-embedding-3-large": {"config-name": "common-config-3", "type": ["embedding"], "use-in": []}, "text-embedding-3-small": {"config-name": "common-config-3", "type": ["embedding"], "use-in": []}, "text-embedding-3-small1": {"config-name": "common-config-36", "type": ["embedding"], "use-in": []}, "text-embedding-ada-002": {"config-name": "common-config-3", "type": ["embedding"], "use-in": []}, "text-multilingual-embedding-002": {"config-name": "common-config-29", "type": ["embedding"], "use-in": []}, "tts-1": {"config-name": "common-config-7", "type": ["voice"], "use-in": []}, "xunfei-deepseek-r1": {"config-name": "common-config-45", "type": ["chat"], "use-in": []}, "GLM-4.5": {"config-name": "common-config-72", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "Kimi-K2": {"config-name": "common-config-73", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "Qwen3-235B-A22B-Instruct-2507": {"config-name": "common-config-74", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "Qwen3-235B-A22B-Thinking-2507": {"config-name": "common-config-75", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "Qwen3-Coder-480B-A35B-Instruct": {"config-name": "common-config-76", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "gpt-5-chat": {"config-name": "common-config-78", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "gpt-5-mini": {"config-name": "common-config-77", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "claude-opus-4-1-20250805": {"config-name": "common-config-65", "use-in": ["gateway"], "type": ["chat"]}, "claude-sonnet-4-20250514": {"config-name": "common-config-65", "use-in": ["gateway"], "type": ["chat"]}, "gpt-oss-120b": {"config-name": "common-config-79", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "gpt-5": {"config-name": "common-config-80", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "DeepSeek-V3.1": {"config-name": "common-config-81", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "gemini-25-flash-image": {"config-name": "common-config-82", "use-in": ["gateway"], "type": ["chat"]}, "gemini-latest-原始": {"config-name": "common-config-14", "use-in": [], "type": ["chat"]}, "gemini-flash-latest-原始": {"config-name": "common-config-14", "use-in": [], "type": ["chat"]}, "gemini-latest": {"config-name": "common-config-69", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "gemini-flash-latest": {"config-name": "common-config-69", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "Qwen3-Next-80B-A3B-Instruct": {"config-name": "common-config-83", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "Qwen3-Next-80B-A3B-Thinking": {"config-name": "common-config-84", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "LongCat-Flash-Chat": {"config-name": "common-config-85", "use-in": ["gateway", "xdk"], "type": ["chat"]}, "Kwai-Kolors/Kolors": {"config-name": "common-config-86", "use-in": [], "type": ["image"]}, "black-forest-labs/FLUX.1-schnell": {"config-name": "common-config-86", "use-in": [], "type": ["image"]}, "black-forest-labs/FLUX.1-dev": {"config-name": "common-config-86", "use-in": [], "type": ["image"]}}, "modelsmap": {"claude-3-5-sonnet": "claude-3-5-sonnet-20240620", "command-r-latest": "command-r-08-2024", "command-r-plus-latest": "command-r-plus-08-2024", "gemini-flash-image-latest": "gemini-2.0-flash-preview-image-generation", "gemini-flash-latest": "gemini-2.5-flash", "gemini-latest": "gemini-2.5-pro", "gemini-vertex-latest": "gemini-2.5-pro", "gpt-4.1@mdb": "g41", "gpt-4.5-preview@mdb": "g45", "gpt-4o-all-自有号池": "gpt-4o", "gpt-4o-all1": "gpt-4o-all", "gpt-4o-all2": "gpt-4o", "gpt-4o-full": "gpt-4o", "gpt-4o-func": "gpt-4o", "gpt-4o-mini-safe": "gpt-4o-mini", "gpt-4o-safe": "gpt-4o", "gpt-4o-tools": "gpt-4o", "o1-mini": "azure-deepseek-r1", "prompt": "gpt-4o", "search": "gpt-4o"}, "providers": {"4chat": {"api_key": "sk-65794a68624763694f694a49557a49314e694a392e65794a755957316c496a6f695569354958316468626d64664d7a67784d31387a4f44457a4969776961574630496a6f784e7a4d324d7a41314d6a63774c434a6c654841694f6a45334d7a63314d5451344e7a42392e623063336f4937535a353134686d556e4432544c4f6448526d38466d6c7a30486c5f4749372d5144516177", "custom_host": "https://api.4chat.me/v1", "provider": "openai"}, "4chat-copy": {"api_key": "sk-65794a68624763694f694a49557a49314e694a392e65794a755957316c496a6f695569354958316468626d64664d7a67784d31387a4f44457a4969776961574630496a6f784e7a4d334e5449304e5449794c434a6c654841694f6a45334e4451334f4449784d6a4a392e4a63454c34487938384369394a53314b4a587a73345f5f314d57634e4a65324c5076434f755444446c436f", "custom_host": "https://api.4chat.me/v1", "override_params": {"model": "claude-3.5-sonnet-20241022"}, "provider": "openai"}, "aigc369": {"api_key": "sk-Q9DYlddwxPKUeQzGDFULUXq34ubKR9hZ6WALXpSaIKXTjulg", "custom_host": "https://api.aigc369.com/v1", "provider": "openai", "test": ["gpt-4o-mini"]}, "aischat": {"api_key": "sk-RvcLGcFNPu3pcPsYyzyLAGl2PyjsoBKYJqZ1Am8UDDVxYNAG", "custom_host": "https://api.aischat.xyz/v1", "provider": "openai", "test": ["gpt-4o-mini"]}, "anthropic": {"api_key": "************************************************************************************************************", "provider": "anthropic"}, "azapi": {"api_key": "a881018", "custom_host": "https://azapi.588886.xyz/imitate/v1", "provider": "openai", "retry": {"attempts": 3}}, "azkimi": {"api_key": "a881018", "custom_host": "http://*************:5471/v1", "provider": "openai"}, "azure_edu_4": {"api_key": "1f90a119444f4b09b63332f2ac83a103", "api_version": "2024-02-15-preview", "deployment_id": "gpt-4", "provider": "azure-openai", "resource_name": "wrhsdopenai"}, "azure_edu_4o": {"api_key": "8e822b7a180e404d9da9961532f18b28", "api_version": "2024-02-15-preview", "deployment_id": "gpt-4o", "provider": "azure-openai", "resource_name": "wrhsdopenaies2"}, "azure_edu_4omini": {"api_key": "1f90a119444f4b09b63332f2ac83a103", "api_version": "2024-02-15-preview", "deployment_id": "gpt-4o-mini", "provider": "azure-openai", "resource_name": "wrhsdopenai", "test": ["gpt-4o-mini"]}, "azure_edu_5_chat": {"api_key": "8e822b7a180e404d9da9961532f18b28", "api_version": "2025-01-01-preview", "deployment_id": "gpt-5-chat", "provider": "azure-openai", "resource_name": "wrhsdopenaies2"}, "azure_edu_5_mini": {"api_key": "8e822b7a180e404d9da9961532f18b28", "api_version": "2025-04-01-preview", "deployment_id": "gpt-5-mini", "provider": "azure-openai", "resource_name": "wrhsdopenaies2"}, "bazi": {"api_key": "a881018", "custom_host": "https://bazi.588886.xyz/v1", "provider": "openai"}, "bing": {"api_key": "a881018", "custom_host": "https://bing.988886.xyz/v1", "provider": "openai"}, "burnhair": {"api_key": "sk-yPUHTp5HKHFeGHicA3C7C5De573d497787B5A90e9fB0DfBe", "custom_host": "https://burn.hair/v1", "provider": "openai"}, "burnhair-retry": {"api_key": "sk-yPUHTp5HKHFeGHicA3C7C5De573d497787B5A90e9fB0DfBe", "custom_host": "https://burn.hair/v1", "provider": "openai", "retry": {"attempts": 3}}, "c-aischat": {"api_key": "sk-erjdUOtI7eMIxSlO7I1ipVPMFyt1vH6y9RxfL9sNiQJWXF4m", "custom_host": "https://cheer.aischat.xyz/v1", "provider": "openai", "test": ["gpt-4o-mini"]}, "cfipyx": {"api_key": "sk-EaZu4yZNkoWDZvZnvvSAKiVMVwXlILHFC23j5Sozn6NUHW8e", "custom_host": "https://api.cfipyx.cn/v1", "provider": "openai"}, "chat01-wrhsdoe": {"api_key": "sk-Sdo7hxbohQQkFSCFkG6kDK0moIo1UrGS46caYUCD368qzxGr", "custom_host": "https://chat01.ai/v1", "provider": "openai"}, "chatanywhere": {"api_key": "sk-1Jx0ZyaVdI1kzVktXrF2Ttpkkr92cQgg5e81ktl4GUgqoIDT", "custom_host": "https://api.chatanywhere.org/v1", "provider": "openai"}, "chutes": {"api_key": "cpk_79bcdb7121ce4f128f522c1e641e9967.4301b096cd6255178601e4ef41e7257c.ZnMbtkOASlodUHmTBH5YBqPeqzAdCVrZ", "custom_host": "https://llm.chutes.ai/v1", "provider": "openai"}, "claude03": {"api_key": "a881018", "custom_host": "http://**************:8008/v1", "provider": "openai"}, "claude04": {"api_key": "a881018", "custom_host": "http://*************:8008/v1", "provider": "openai"}, "claude05": {"api_key": "a881018", "custom_host": "http://************:8008/v1", "provider": "openai"}, "claude06": {"api_key": "a881018", "custom_host": "http://*************:8008/v1", "provider": "openai"}, "cloudflare": {"api_key": "****************************************", "provider": "workers-ai", "workers_ai_account_id": "192d22b79aae0085b7bc95099ea1abae"}, "cohere": {"api_key": "9PAmkcfrPXaVnG6WrNhbguxSxqVtK3KrOFAUQSqU", "provider": "cohere"}, "crewdle@3.7": {"api_key": "e2DfWPKL+?$!+pQ]LL7t,0f.i];^:", "custom_host": "https://api.crewdle.ai/workflows/8T8KksXFhiSRdP5zFrmT", "provider": "openai"}, "crond": {"api_key": "sk-iEokpXAc1yfxbKk4pqtiU2MqcEafC3ORDtE0kRaRy8xaDfpJ", "custom_host": "https://api.crond.dev/v1", "provider": "openai"}, "cymru": {"api_key": "sk-YQnY5wUA2ZhuclzVBcD570Ad228648Fb8e05Df1a23FeFf64", "custom_host": "https://api.cymru/v1", "provider": "openai"}, "deepinfra": {"api_key": "oXrLGuq7vfYrqC2KVIHeKoGfP8D0DbGQ", "custom_host": "https://api.deepinfra.com/v1/openai", "provider": "openai"}, "deepseek": {"api_key": "a881018", "custom_host": "http://deep.frp.988886.xyz:8080/v1", "provider": "openai"}, "deepseek-api": {"api_key": "***********************************", "custom_host": "https://api.deepseek.com/v1", "provider": "openai", "test": []}, "electronhub": {"api_key": "ek-hlEBvHF0dEcyx28SYZlN4wSt1IwAlYbfe1ufWkEtazcGxJG6TV", "custom_host": "https://api.electronhub.top/v1", "provider": "openai", "test": ["gpt-4o-mini"]}, "ephone": {"api_key": "sk-Rl48COO1UNOhhsQD4HtwGDvqWSPKaX02mzPqO5d4u1KJMhgU", "custom_host": "https://api.ephone.ai/v1", "provider": "openai", "test": ["gpt-4o-mini"]}, "ephone-offical": {"api_key": "sk-9wBPnC8qmJrHCmtsQWEzQMtO48h1JqOOcZ8ZFUi2R2II9jUG", "custom_host": "https://api.ephone.ai/v1", "provider": "openai"}, "fal": {"api_key": "a881018", "custom_host": "http://*************:4117/v1", "provider": "openai"}, "flux": {"api_key": "a881018", "custom_host": "https://flux.588886.xyz/v1", "provider": "openai"}, "gemini": {"api_key": "AIzaSyBR6opNn1J8vxV7yYc8NO9gb6n-RCbvZhM", "provider": "google"}, "gemini-copy": {"provider": "openai", "api_key": "a881018", "custom_host": "http://*************:8001/v1"}, "gemini2": {"api_key": "AIzaSyCfGdkJ4p6XUZhvPNpujhcjPTI6Lv8g-S8", "provider": "google"}, "gemini3": {"api_key": "AIzaSyBtZMZ2o0zTtw3iw1OnoXUGn9Wja0SbsJ0", "provider": "google"}, "gemini4": {"api_key": "AIzaSyBpATfZ0qCjZL9QAMJrdUWZPv4oRpWvFHk", "provider": "google"}, "gemini5": {"api_key": "AIzaSyAlL8YjgDloS1L4Hr0NmplglbVygr8HiYI", "provider": "google"}, "gemini6": {"api_key": "AIzaSyAXAwTzOcRhFO4pELIkyFyTCGag8ifWtnk", "provider": "google"}, "gemini7": {"api_key": "AIzaSyD7s8royWXQOxoGMX4ZSq6ZZw4ryJCVkvM", "provider": "google"}, "gemini8": {"api_key": "AIzaSyDCvVz3ZbX-zw-hjUME_DS0K5VxLWEaVMo", "provider": "google"}, "genspark": {"api_key": "a881018", "custom_host": "http://*************:7055/v1", "provider": "openai"}, "github": {"provider": "openai", "api_key": "****************************************", "custom_host": "https://models.inference.ai.azure.com"}, "github1": {"api_key": "****************************************", "custom_host": "https://models.inference.ai.azure.com", "provider": "openai"}, "github2": {"api_key": "****************************************", "custom_host": "https://models.inference.ai.azure.com", "provider": "openai"}, "github3": {"api_key": "****************************************", "custom_host": "https://models.inference.ai.azure.com", "provider": "openai"}, "grok-api": {"api_key": "************************************************************************************", "custom_host": "https://api.x.ai/v1", "provider": "openai"}, "groq": {"api_key": "********************************************************", "custom_host": "https://api.groq.com/openai/v1", "provider": "openai", "test": ["llama-3.1-8b-instant"]}, "gueai": {"api_key": "sk-tRGk1aIxiCKxolP7XyUdk99WkBHafDI6QUesTLGd5nl5tHwB", "custom_host": "https://api.gueai.com/v1", "provider": "openai", "test": ["gpt-4o-mini"]}, "gueai-default": {"api_key": "sk-9AcLsKNXrVXXirpNgsgzqeWa2gIfC6pOGfBeXsFuFVWPRjIK", "custom_host": "https://api.gueai.com/v1", "provider": "openai", "test": ["gpt-4o-mini"]}, "gueai-nixiang": {"api_key": "sk-l8eP15jSvqiqlafgE8rc2I75feMwlzKK0VLIkuI7TdL0dQvT", "custom_host": "https://api.gueai.com/v1", "provider": "openai"}, "gueai1": {"api_key": "sk-XCiTv0n0VCDFCzdQk6Wg37P6KXWZGOtBQBoFhWDuu0hlmRms", "custom_host": "https://api.gueai.com/v1", "provider": "openai"}, "ham1mini": {"api_key": "sk-ayu3oPHWc85UuWiMAf6a815eB1Ab468b80Fc411b7529Bc21", "custom_host": "https://ham1mini.588886.xyz/v1", "provider": "openai"}, "ham2mini": {"api_key": "sk-zMkVVKxUPN96WAJhA0Fb0531D7194b2fBa02754d1bF8325e", "custom_host": "https://ham2mini.588886.xyz/v1", "provider": "openai"}, "hamA1-grok": {"api_key": "a881018", "custom_host": "http://*************:4419/v1", "provider": "openai"}, "haochi": {"api_key": "a881018", "custom_host": "http://*************:7769/v1", "provider": "openai"}, "hunyuan": {"api_key": "sk-nFPPPNr9t40dPMlwmayu6ePEyYl3c0raYlpA1VnG4colGQFI", "custom_host": "https://api.hunyuan.cloud.tencent.com/v1", "provider": "openai", "test": ["hunyuan-lite"]}, "huoshan": {"api_key": "d2aff37c-babc-4f12-822e-d138ec47a46d", "custom_host": "https://ark.cn-beijing.volces.com/api/v3", "provider": "openai"}, "hyw1mini": {"api_key": "sk-lAyqEKGRWKoBcE0UCa70E0AdD5C248F1A9Fa1e010a9aE5C3", "custom_host": "https://hyw1mini.588886.xyz/v1", "provider": "openai"}, "internlm": {"api_key": "eyJ0eXBlIjoiSldUIiwiYWxnIjoiSFM1MTIifQ.********************************************************************************************************************************************************************************************************************************************************************************.ggGbBAyfZu53JnzedhKxSQtMHyK8TQqqq0_lAV-Kf3jfvh7UMRbrLl8vFhSHPI48MjsXzrK4_I6hLDZGlQhDRg", "custom_host": "https://internlm-chat.intern-ai.org.cn/puyu/api/v1", "provider": "openai", "test": ["internlm2.5-latest"]}, "itsfurry": {"api_key": "sk-cdsFfUOc5BYMGAC353F2DbD9757f4eF8Aa3f8378Ae174303", "custom_host": "https://oai.furryapi.org/v1", "provider": "openai", "test": ["gpt-4o-mini"]}, "jpny": {"api_key": "sk-G3EHVTP2JobSCu5FkmKMopV1uNdD5N91uIBEVIVLt7wDEoFW", "custom_host": "https://api.jpny.top/v1", "provider": "openai", "test": ["gpt-4o-mini"]}, "julep": {"api_key": "a881018", "custom_host": "http://*************:4311/v1", "provider": "openai"}, "libre": {"api_key": "a881018", "custom_host": "https://libreapi.588886.xyz/v1", "provider": "openai"}, "lzj2mini": {"api_key": "sk-dDE01Ws1LEgBlSxB9d391aD5F1534dD9Ba4f8dBa23038e67", "custom_host": "https://lzj2mini.588886.xyz/v1", "provider": "openai"}, "mdb": {"api_key": "mdb_0uWIykFnyJpFdavfqFcE4agrpLbl5Vf8vmQtiR4Jc2G1", "custom_host": "https://llm.mdb.ai", "provider": "openai"}, "mistral": {"api_key": "jHxtbLBPgOjlM8ySCfJjdeLCs7Bknmg3", "provider": "mistral-ai"}, "modelscope1": {"provider": "openai", "api_key": "ms-676409d5-49e6-4b4a-8aa2-a558c6124c88", "custom_host": "https://api-inference.modelscope.cn/v1"}, "mult-r1": {"api_key": "a881018", "custom_host": "http://*************:9476/v1", "provider": "openai"}, "nebius": {"api_key": "eyJhbGciOiJIUzI1NiIsImtpZCI6IlV6SXJWd1h0dnprLVRvdzlLZWstc0M1akptWXBvX1VaVkxUZlpnMDRlOFUiLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RHocGBM8SrtXdFVqvRQ9Wmz8j22hzEYzIoIwWJNxeOs", "custom_host": "https://api.studio.nebius.ai/v1", "provider": "openai"}, "openai": {"api_key": "********************************************************************************************************************************************************************", "custom_host": "https://api.openai.com/v1", "provider": "openai"}, "openrouter": {"api_key": "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", "custom_host": "https://openrouter.ai/api/v1", "provider": "openai"}, "openxs": {"api_key": "sk-rp3JJQJwzg2SpqSomnIq2TchCtYwXaJz7g0jb7PIuywZNKSp", "custom_host": "https://api.openxs.top/v1", "provider": "openai"}, "perplexity": {"api_key": "a881018", "custom_host": "https://perplexity.588886.xyz/v1", "provider": "openai"}, "perplexity1": {"api_key": "a881018", "custom_host": "https://perplexity1.588886.xyz/v1", "provider": "openai"}, "searx": {"api_key": "a881018", "custom_host": "https://searx.588886.xyz/v1", "provider": "openai", "retry": {"attempts": 3}}, "siliconflow1": {"provider": "openai", "api_key": "sk-aoykchlfqbhjyaazabmshqcmonygdezdnlmupytgxxbrbdte", "custom_host": "https://api.siliconflow.cn/v1"}, "sophnet1": {"provider": "openai", "api_key": "t6xzEgOzBX_0XeJ38VuNFShyPQPsYa9FVAAtOSFl5Wp3AiiB74OMHUOAexX5wTQfZ_wAZSbT4cv0pcuNu6jv5g", "custom_host": "https://www.sophnet.com/api/open-apis/v1"}, "sophnet2": {"provider": "openai", "api_key": "Z8KeSW4r4kwjGuL7dCvy74UrqbpL3CXNsex67jzEyRdOOGuV0GcsZMqoEumed0I-WNvbBGAUIIMTvAyhlv6L8Q", "custom_host": "https://www.sophnet.com/api/open-apis/v1"}, "spark": {"api_key": "lFvZTjkBJOvUDUmJMDnn:ihGKVeQIFIfRafSNFpec", "custom_host": "https://spark-api-open.xf-yun.com/v1", "provider": "openai"}, "vertex-ai": {"custom_host": "https://us-central1-aiplatform.googleapis.com", "override_params": {"max_tokens": 8192}, "provider": "vertex-ai", "retry": {"attempts": 3}, "vertex_project_id": "stellar-psyche-463914-d7", "vertex_region": "us-central1"}, "voapi": {"api_key": "sk-6K9iJ8ihFd4kPDJMwB7I9JfjwwmXoeJ0TPWKzMoKHe1J9TT5", "custom_host": "https://demo.voapi.top/v1", "provider": "openai", "test": ["gpt-4o-mini", "gpt-4o", "gpt-4-turbo", "dall-e-3", "claude-3-5-sonnet-20240620"]}, "voapi-default": {"api_key": "sk-AICppJzXxT3POoxi9u2zdENh9Y9NUBpB4pTJgUDUqog7CAZr", "custom_host": "https://demo.voapi.top/v1", "provider": "openai", "test": ["gpt-4o-mini", "gpt-4o", "gpt-4-turbo", "dall-e-3", "claude-3-5-sonnet-20240620"]}, "voct": {"api_key": "sk-zKZTWMWixsgdjU94m2jZ7O50KrMb5LNvU53CuAOpaendI4ZK", "custom_host": "https://api.voct.org/v1", "provider": "openai"}, "wochirou1": {"api_key": "sk-a5PlpJXifGfgqHOznekBu4cBMXVzNg8bBIWX4SxmdJ22TR5o", "custom_host": "https://api.wochirou.com/v1", "provider": "openai", "test": ["gpt-4o-mini"]}, "wochirou2": {"api_key": "sk-ow1dRo44HeukYfpt78B46c7bB672419cBeF50645259b328a", "custom_host": "https://api.wochirou.com/v1", "provider": "openai", "test": ["gpt-4o-mini"]}, "x-ai": {"api_key": "************************************************************************************", "provider": "x-ai"}, "yunwu": {"api_key": "sk-hOZVBGXLLl9npe9c3pMe0RGxaj7UYWyTT8JCqgUZKA", "custom_host": "https://free.yunwu.ai/v1", "provider": "openai"}, "zhipu": {"api_key": "8dfd7a3416d21f5e61b70504816b87f9.QtDsYinCYwZCw15T", "custom_host": "https://open.bigmodel.cn/api/paas/v4", "provider": "openai"}, "cerebas": {"provider": "openai", "api_key": "csk-3644w5tyndw5xppwhpm98mrvf6p4en93cdkfw624rw8rvtcw", "custom_host": "https://api.cerebras.ai/v1"}, "akash": {"provider": "openai", "api_key": "sk-FEPVoA4n2OLaAfFyff5oSQ", "custom_host": "https://chatapi.akash.network/api/v1"}, "fal-gemini": {"provider": "openai", "api_key": "aA881018", "custom_host": "http://*************:5983/v1"}}, "custom_model_types": [], "custom_trans_types": []}