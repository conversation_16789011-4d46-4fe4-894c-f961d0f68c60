# 简化版 LLM 网关服务器

这是一个简化版本的 LLM 网关服务器，只保留了最初的核心功能：根据用户输入的模型从配置文件中读取配置，然后向后端转发请求。

## 功能特点

- ✅ 支持多个网关路径（从配置文件读取）
- ✅ 模型配置解析和映射
- ✅ 支持复杂的配置结构（base_provider、targets等）
- ✅ 请求转发到后端网关
- ✅ 流式响应支持
- ✅ 基本的日志记录
- ✅ CORS 支持
- ❌ 移除了搜索功能
- ❌ 移除了思考模式
- ❌ 移除了内容转换器
- ❌ 移除了复杂的文件上传处理
- ❌ 移除了详细的日志文件写入

## 安装和运行

1. 安装依赖：
```bash
npm install
```

2. 确保配置文件存在：
```
/root/test/gateway/conf.json
```

3. 启动服务器：
```bash
npm start
```

服务器将在端口 3000 上运行（可通过环境变量 PORT 修改）。

## 配置文件结构

服务器会读取 `/root/test/gateway/conf.json` 配置文件，支持以下结构：

### 基本结构
```json
{
  "gateway_paths": ["gateway", "test", "xdk"],
  "models": {
    "model-name": {
      "config-name": "common-config-1",
      "use-in": ["gateway"],
      "type": ["chat"]
    }
  },
  "modelsmap": {
    "old-model-name": "new-model-name"
  },
  "configurations": {
    "common-config-1": {
      "strategy": {"mode": "fallback"},
      "targets": ["provider1", "provider2"]
    }
  },
  "providers": {
    "provider1": {
      "api_key": "your-api-key",
      "base_url": "https://api.example.com"
    }
  }
}
```

## API 端点

### 获取模型列表
```
GET /{gateway_path}/v1/models
Authorization: Bearer {your-token}
```

### 聊天完成
```
POST /{gateway_path}/v1/chat/completions
Authorization: Bearer {your-token}
Content-Type: application/json

{
  "model": "model-name",
  "messages": [...],
  "stream": true
}
```

## 授权

使用 Bearer token 进行授权，支持的 token：
- `a881018` (key-1)
- `a881018dddd` (key-2)

## 日志

服务器会在控制台输出简单的请求日志，格式：
```
[时间戳] 状态码 | 客户端IP | 网关密钥 | 模型名称
```

## 与原版本的区别

这个简化版本移除了所有复杂的功能，专注于核心的请求转发功能：

1. **保留的功能**：
   - 基本的模型配置解析
   - 请求转发到后端
   - 流式响应支持
   - 模型映射
   - 多网关路径支持

2. **移除的功能**：
   - R1 思考模式处理
   - 在线搜索功能
   - 内容转换器
   - 复杂的文件上传处理
   - 详细的日志文件管理
   - 心跳机制
   - 复杂的错误处理和重试

这使得代码更加简洁易懂，适合作为基础版本使用。
